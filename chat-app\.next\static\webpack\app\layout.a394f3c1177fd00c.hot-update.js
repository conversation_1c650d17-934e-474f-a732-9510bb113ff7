"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/contexts/SocketContext.tsx":
/*!****************************************!*\
  !*** ./src/contexts/SocketContext.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SocketProvider: () => (/* binding */ SocketProvider),\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ SocketProvider,useSocket auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst SocketContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction SocketProvider(param) {\n    let { children } = param;\n    _s();\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [onlineUsers, setOnlineUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [typingUsers, setTypingUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SocketProvider.useEffect\": ()=>{\n            if (user) {\n                const socketUrl = \"http://localhost:3001\" || 0;\n                const newSocket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(socketUrl, {\n                    auth: {\n                        userId: user.id,\n                        username: user.username\n                    }\n                });\n                setSocket(newSocket);\n                // Listen for online users updates\n                newSocket.on('users:online', {\n                    \"SocketProvider.useEffect\": (users)=>{\n                        setOnlineUsers(users);\n                    }\n                }[\"SocketProvider.useEffect\"]);\n                // Listen for typing indicators\n                newSocket.on('typing:start', {\n                    \"SocketProvider.useEffect\": (param)=>{\n                        let { roomId, userId, username } = param;\n                        setTypingUsers({\n                            \"SocketProvider.useEffect\": (prev)=>({\n                                    ...prev,\n                                    [roomId]: [\n                                        ...(prev[roomId] || []).filter({\n                                            \"SocketProvider.useEffect\": (u)=>u !== username\n                                        }[\"SocketProvider.useEffect\"]),\n                                        username\n                                    ]\n                                })\n                        }[\"SocketProvider.useEffect\"]);\n                    }\n                }[\"SocketProvider.useEffect\"]);\n                newSocket.on('typing:stop', {\n                    \"SocketProvider.useEffect\": (param)=>{\n                        let { roomId, userId, username } = param;\n                        setTypingUsers({\n                            \"SocketProvider.useEffect\": (prev)=>({\n                                    ...prev,\n                                    [roomId]: (prev[roomId] || []).filter({\n                                        \"SocketProvider.useEffect\": (u)=>u !== username\n                                    }[\"SocketProvider.useEffect\"])\n                                })\n                        }[\"SocketProvider.useEffect\"]);\n                    }\n                }[\"SocketProvider.useEffect\"]);\n                // Clean up typing indicators after timeout\n                newSocket.on('typing:timeout', {\n                    \"SocketProvider.useEffect\": (param)=>{\n                        let { roomId, username } = param;\n                        setTypingUsers({\n                            \"SocketProvider.useEffect\": (prev)=>({\n                                    ...prev,\n                                    [roomId]: (prev[roomId] || []).filter({\n                                        \"SocketProvider.useEffect\": (u)=>u !== username\n                                    }[\"SocketProvider.useEffect\"])\n                                })\n                        }[\"SocketProvider.useEffect\"]);\n                    }\n                }[\"SocketProvider.useEffect\"]);\n                return ({\n                    \"SocketProvider.useEffect\": ()=>{\n                        newSocket.close();\n                    }\n                })[\"SocketProvider.useEffect\"];\n            }\n        }\n    }[\"SocketProvider.useEffect\"], [\n        user\n    ]);\n    const joinRoom = (roomId)=>{\n        if (socket) {\n            socket.emit('room:join', roomId);\n        }\n    };\n    const leaveRoom = (roomId)=>{\n        if (socket) {\n            socket.emit('room:leave', roomId);\n        }\n    };\n    const sendMessage = (message)=>{\n        if (socket) {\n            socket.emit('message:send', message);\n        }\n    };\n    const sendPrivateMessage = (message)=>{\n        if (socket) {\n            socket.emit('message:private', message);\n        }\n    };\n    const sendTyping = (roomId, isTyping)=>{\n        if (socket) {\n            if (isTyping) {\n                socket.emit('typing:start', roomId);\n            } else {\n                socket.emit('typing:stop', roomId);\n            }\n        }\n    };\n    const value = {\n        socket,\n        onlineUsers,\n        typingUsers,\n        joinRoom,\n        leaveRoom,\n        sendMessage,\n        sendTyping,\n        sendPrivateMessage\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SocketContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\contexts\\\\SocketContext.tsx\",\n        lineNumber: 118,\n        columnNumber: 10\n    }, this);\n}\n_s(SocketProvider, \"A3t8zimq3DcKdnvr2pXoSEL7MyY=\", false, function() {\n    return [\n        _AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = SocketProvider;\nfunction useSocket() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SocketContext);\n    if (context === undefined) {\n        throw new Error('useSocket must be used within a SocketProvider');\n    }\n    return context;\n}\n_s1(useSocket, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"SocketProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/SocketContext.tsx\n"));

/***/ })

});