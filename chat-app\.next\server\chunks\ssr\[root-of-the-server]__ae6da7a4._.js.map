{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Chat%20Application/chat-app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co'\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo_key'\n\n// Check if we're in demo mode\nconst isDemoMode = supabaseUrl === 'https://demo.supabase.co'\n\n// Client-side Supabase client (mock for demo mode)\nexport const supabase = isDemoMode\n  ? createMockSupabaseClient()\n  : createBrowserClient(supabaseUrl, supabaseAnonKey)\n\n// Server-side Supabase client (for API routes)\nexport const supabaseAdmin = isDemoMode\n  ? createMockSupabaseClient()\n  : createClient(supabaseUrl, process.env.SUPABASE_SERVICE_ROLE_KEY!)\n\n// Mock Supabase client for demo mode\nfunction createMockSupabaseClient() {\n  return {\n    auth: {\n      getSession: () => Promise.resolve({ data: { session: null }, error: null }),\n      onAuthStateChange: (callback: any) => {\n        // Return a mock subscription\n        return { data: { subscription: { unsubscribe: () => {} } } }\n      },\n      signInWithPassword: () => Promise.resolve({ error: { message: 'Demo mode: Please set up Supabase to enable authentication' } }),\n      signUp: () => Promise.resolve({ error: { message: 'Demo mode: Please set up Supabase to enable authentication' } }),\n      signOut: () => Promise.resolve({ error: null })\n    },\n    from: (table: string) => ({\n      select: () => ({\n        eq: () => ({\n          single: () => Promise.resolve({ data: null, error: { message: 'Demo mode: Please set up Supabase database' } }),\n          order: () => ({\n            limit: () => Promise.resolve({ data: [], error: null })\n          })\n        }),\n        order: () => ({\n          limit: () => Promise.resolve({ data: [], error: null })\n        }),\n        limit: () => Promise.resolve({ data: [], error: null })\n      }),\n      insert: () => ({\n        select: () => ({\n          single: () => Promise.resolve({ data: null, error: { message: 'Demo mode: Please set up Supabase database' } })\n        })\n      }),\n      update: () => ({\n        eq: () => Promise.resolve({ error: { message: 'Demo mode: Please set up Supabase database' } })\n      })\n    })\n  } as any\n}\n\n// Database types\nexport interface User {\n  id: string\n  email: string\n  username: string\n  avatar_url?: string\n  status: 'online' | 'offline' | 'away'\n  last_seen: string\n  created_at: string\n}\n\nexport interface ChatRoom {\n  id: string\n  name: string\n  description?: string\n  type: 'public' | 'private'\n  created_by: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface Message {\n  id: string\n  content: string\n  user_id: string\n  room_id?: string\n  private_to?: string\n  message_type: 'text' | 'image' | 'file' | 'video'\n  file_url?: string\n  file_name?: string\n  file_size?: number\n  created_at: string\n  updated_at: string\n  reactions?: MessageReaction[]\n}\n\nexport interface MessageReaction {\n  id: string\n  message_id: string\n  user_id: string\n  emoji: string\n  created_at: string\n}\n\nexport interface RoomMember {\n  id: string\n  room_id: string\n  user_id: string\n  role: 'admin' | 'moderator' | 'member'\n  joined_at: string\n}\n\nexport interface MediaFile {\n  id: string\n  user_id: string\n  file_name: string\n  file_url: string\n  file_type: string\n  file_size: number\n  message_id?: string\n  created_at: string\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,cAAc,gEAAwC;AAC5D,MAAM,kBAAkB,gDAA6C;AAErE,8BAA8B;AAC9B,MAAM,aAAa,gBAAgB;AAG5B,MAAM,WAAW,uCACpB;AAIG,MAAM,gBAAgB,uCACzB;AAGJ,qCAAqC;AACrC,SAAS;IACP,OAAO;QACL,MAAM;YACJ,YAAY,IAAM,QAAQ,OAAO,CAAC;oBAAE,MAAM;wBAAE,SAAS;oBAAK;oBAAG,OAAO;gBAAK;YACzE,mBAAmB,CAAC;gBAClB,6BAA6B;gBAC7B,OAAO;oBAAE,MAAM;wBAAE,cAAc;4BAAE,aAAa,KAAO;wBAAE;oBAAE;gBAAE;YAC7D;YACA,oBAAoB,IAAM,QAAQ,OAAO,CAAC;oBAAE,OAAO;wBAAE,SAAS;oBAA6D;gBAAE;YAC7H,QAAQ,IAAM,QAAQ,OAAO,CAAC;oBAAE,OAAO;wBAAE,SAAS;oBAA6D;gBAAE;YACjH,SAAS,IAAM,QAAQ,OAAO,CAAC;oBAAE,OAAO;gBAAK;QAC/C;QACA,MAAM,CAAC,QAAkB,CAAC;gBACxB,QAAQ,IAAM,CAAC;wBACb,IAAI,IAAM,CAAC;gCACT,QAAQ,IAAM,QAAQ,OAAO,CAAC;wCAAE,MAAM;wCAAM,OAAO;4CAAE,SAAS;wCAA6C;oCAAE;gCAC7G,OAAO,IAAM,CAAC;wCACZ,OAAO,IAAM,QAAQ,OAAO,CAAC;gDAAE,MAAM,EAAE;gDAAE,OAAO;4CAAK;oCACvD,CAAC;4BACH,CAAC;wBACD,OAAO,IAAM,CAAC;gCACZ,OAAO,IAAM,QAAQ,OAAO,CAAC;wCAAE,MAAM,EAAE;wCAAE,OAAO;oCAAK;4BACvD,CAAC;wBACD,OAAO,IAAM,QAAQ,OAAO,CAAC;gCAAE,MAAM,EAAE;gCAAE,OAAO;4BAAK;oBACvD,CAAC;gBACD,QAAQ,IAAM,CAAC;wBACb,QAAQ,IAAM,CAAC;gCACb,QAAQ,IAAM,QAAQ,OAAO,CAAC;wCAAE,MAAM;wCAAM,OAAO;4CAAE,SAAS;wCAA6C;oCAAE;4BAC/G,CAAC;oBACH,CAAC;gBACD,QAAQ,IAAM,CAAC;wBACb,IAAI,IAAM,QAAQ,OAAO,CAAC;gCAAE,OAAO;oCAAE,SAAS;gCAA6C;4BAAE;oBAC/F,CAAC;YACH,CAAC;IACH;AACF", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Chat%20Application/chat-app/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User as SupabaseUser } from '@supabase/supabase-js'\nimport { supabase, User } from '@/lib/supabase'\nimport { useRouter } from 'next/navigation'\n\ninterface AuthContextType {\n  user: User | null\n  supabaseUser: SupabaseUser | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<{ error?: string }>\n  signUp: (email: string, password: string, username: string) => Promise<{ error?: string }>\n  signOut: () => Promise<void>\n  updateProfile: (updates: Partial<User>) => Promise<{ error?: string }>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null)\n  const [loading, setLoading] = useState(true)\n  const router = useRouter()\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession()\n      if (session?.user) {\n        setSupabaseUser(session.user)\n        await fetchUserProfile(session.user.id)\n      }\n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (session?.user) {\n          setSupabaseUser(session.user)\n          await fetchUserProfile(session.user.id)\n        } else {\n          setSupabaseUser(null)\n          setUser(null)\n        }\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const fetchUserProfile = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .eq('id', userId)\n        .single()\n\n      if (error) throw error\n      setUser(data)\n    } catch (error) {\n      console.error('Error fetching user profile:', error)\n    }\n  }\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      const { error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n\n      if (error) return { error: error.message }\n      return {}\n    } catch (error) {\n      return { error: 'An unexpected error occurred' }\n    }\n  }\n\n  const signUp = async (email: string, password: string, username: string) => {\n    try {\n      const { error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            username,\n          },\n        },\n      })\n\n      if (error) return { error: error.message }\n      return {}\n    } catch (error) {\n      return { error: 'An unexpected error occurred' }\n    }\n  }\n\n  const signOut = async () => {\n    await supabase.auth.signOut()\n    setUser(null)\n    setSupabaseUser(null)\n    router.push('/auth/login')\n  }\n\n  const updateProfile = async (updates: Partial<User>) => {\n    if (!user) return { error: 'No user logged in' }\n\n    try {\n      const { error } = await supabase\n        .from('users')\n        .update({ ...updates, updated_at: new Date().toISOString() })\n        .eq('id', user.id)\n\n      if (error) throw error\n\n      setUser({ ...user, ...updates })\n      return {}\n    } catch (error) {\n      return { error: 'Failed to update profile' }\n    }\n  }\n\n  const value = {\n    user,\n    supabaseUser,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n    updateProfile,\n  }\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AALA;;;;;AAiBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sBAAsB;QACtB,MAAM,oBAAoB;YACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YAC5D,IAAI,SAAS,MAAM;gBACjB,gBAAgB,QAAQ,IAAI;gBAC5B,MAAM,iBAAiB,QAAQ,IAAI,CAAC,EAAE;YACxC;YACA,WAAW;QACb;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,IAAI,SAAS,MAAM;gBACjB,gBAAgB,QAAQ,IAAI;gBAC5B,MAAM,iBAAiB,QAAQ,IAAI,CAAC,EAAE;YACxC,OAAO;gBACL,gBAAgB;gBAChB,QAAQ;YACV;YACA,WAAW;QACb;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG,EAAE;IAEL,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBACvD;gBACA;YACF;YAEA,IAAI,OAAO,OAAO;gBAAE,OAAO,MAAM,OAAO;YAAC;YACzC,OAAO,CAAC;QACV,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;YAA+B;QACjD;IACF;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC3C;gBACA;gBACA,SAAS;oBACP,MAAM;wBACJ;oBACF;gBACF;YACF;YAEA,IAAI,OAAO,OAAO;gBAAE,OAAO,MAAM,OAAO;YAAC;YACzC,OAAO,CAAC;QACV,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;YAA+B;QACjD;IACF;IAEA,MAAM,UAAU;QACd,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAC3B,QAAQ;QACR,gBAAgB;QAChB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,MAAM,OAAO;YAAE,OAAO;QAAoB;QAE/C,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC;gBAAE,GAAG,OAAO;gBAAE,YAAY,IAAI,OAAO,WAAW;YAAG,GAC1D,EAAE,CAAC,MAAM,KAAK,EAAE;YAEnB,IAAI,OAAO,MAAM;YAEjB,QAAQ;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC;YAC9B,OAAO,CAAC;QACV,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;YAA2B;QAC7C;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Chat%20Application/chat-app/src/contexts/SocketContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { io, Socket } from 'socket.io-client'\nimport { useAuth } from './AuthContext'\nimport { Message, User } from '@/lib/supabase'\n\ninterface SocketContextType {\n  socket: Socket | null\n  onlineUsers: User[]\n  typingUsers: { [roomId: string]: string[] }\n  joinRoom: (roomId: string) => void\n  leaveRoom: (roomId: string) => void\n  sendMessage: (message: Omit<Message, 'id' | 'created_at' | 'updated_at'>) => void\n  sendTyping: (roomId: string, isTyping: boolean) => void\n  sendPrivateMessage: (message: Omit<Message, 'id' | 'created_at' | 'updated_at'>) => void\n}\n\nconst SocketContext = createContext<SocketContextType | undefined>(undefined)\n\nexport function SocketProvider({ children }: { children: React.ReactNode }) {\n  const [socket, setSocket] = useState<Socket | null>(null)\n  const [onlineUsers, setOnlineUsers] = useState<User[]>([])\n  const [typingUsers, setTypingUsers] = useState<{ [roomId: string]: string[] }>({})\n  const { user } = useAuth()\n\n  useEffect(() => {\n    if (user) {\n      const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3001'\n      const newSocket = io(socketUrl, {\n        auth: {\n          userId: user.id,\n          username: user.username,\n        },\n      })\n\n      setSocket(newSocket)\n\n      // Listen for online users updates\n      newSocket.on('users:online', (users: User[]) => {\n        setOnlineUsers(users)\n      })\n\n      // Listen for typing indicators\n      newSocket.on('typing:start', ({ roomId, userId, username }) => {\n        setTypingUsers(prev => ({\n          ...prev,\n          [roomId]: [...(prev[roomId] || []).filter(u => u !== username), username]\n        }))\n      })\n\n      newSocket.on('typing:stop', ({ roomId, userId, username }) => {\n        setTypingUsers(prev => ({\n          ...prev,\n          [roomId]: (prev[roomId] || []).filter(u => u !== username)\n        }))\n      })\n\n      // Clean up typing indicators after timeout\n      newSocket.on('typing:timeout', ({ roomId, username }) => {\n        setTypingUsers(prev => ({\n          ...prev,\n          [roomId]: (prev[roomId] || []).filter(u => u !== username)\n        }))\n      })\n\n      return () => {\n        newSocket.close()\n      }\n    }\n  }, [user])\n\n  const joinRoom = (roomId: string) => {\n    if (socket) {\n      socket.emit('room:join', roomId)\n    }\n  }\n\n  const leaveRoom = (roomId: string) => {\n    if (socket) {\n      socket.emit('room:leave', roomId)\n    }\n  }\n\n  const sendMessage = (message: Omit<Message, 'id' | 'created_at' | 'updated_at'>) => {\n    if (socket) {\n      socket.emit('message:send', message)\n    }\n  }\n\n  const sendPrivateMessage = (message: Omit<Message, 'id' | 'created_at' | 'updated_at'>) => {\n    if (socket) {\n      socket.emit('message:private', message)\n    }\n  }\n\n  const sendTyping = (roomId: string, isTyping: boolean) => {\n    if (socket) {\n      if (isTyping) {\n        socket.emit('typing:start', roomId)\n      } else {\n        socket.emit('typing:stop', roomId)\n      }\n    }\n  }\n\n  const value = {\n    socket,\n    onlineUsers,\n    typingUsers,\n    joinRoom,\n    leaveRoom,\n    sendMessage,\n    sendTyping,\n    sendPrivateMessage,\n  }\n\n  return <SocketContext.Provider value={value}>{children}</SocketContext.Provider>\n}\n\nexport function useSocket() {\n  const context = useContext(SocketContext)\n  if (context === undefined) {\n    throw new Error('useSocket must be used within a SocketProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAkBA,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAiC;AAE5D,SAAS,eAAe,EAAE,QAAQ,EAAiC;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkC,CAAC;IAChF,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,MAAM,YAAY,6DAAsC;YACxD,MAAM,YAAY,CAAA,GAAA,wLAAA,CAAA,KAAE,AAAD,EAAE,WAAW;gBAC9B,MAAM;oBACJ,QAAQ,KAAK,EAAE;oBACf,UAAU,KAAK,QAAQ;gBACzB;YACF;YAEA,UAAU;YAEV,kCAAkC;YAClC,UAAU,EAAE,CAAC,gBAAgB,CAAC;gBAC5B,eAAe;YACjB;YAEA,+BAA+B;YAC/B,UAAU,EAAE,CAAC,gBAAgB,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE;gBACxD,eAAe,CAAA,OAAQ,CAAC;wBACtB,GAAG,IAAI;wBACP,CAAC,OAAO,EAAE;+BAAI,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,MAAM;4BAAW;yBAAS;oBAC3E,CAAC;YACH;YAEA,UAAU,EAAE,CAAC,eAAe,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE;gBACvD,eAAe,CAAA,OAAQ,CAAC;wBACtB,GAAG,IAAI;wBACP,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,MAAM;oBACnD,CAAC;YACH;YAEA,2CAA2C;YAC3C,UAAU,EAAE,CAAC,kBAAkB,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE;gBAClD,eAAe,CAAA,OAAQ,CAAC;wBACtB,GAAG,IAAI;wBACP,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,MAAM;oBACnD,CAAC;YACH;YAEA,OAAO;gBACL,UAAU,KAAK;YACjB;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,WAAW,CAAC;QAChB,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,aAAa;QAC3B;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,cAAc;QAC5B;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,gBAAgB;QAC9B;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,mBAAmB;QACjC;IACF;IAEA,MAAM,aAAa,CAAC,QAAgB;QAClC,IAAI,QAAQ;YACV,IAAI,UAAU;gBACZ,OAAO,IAAI,CAAC,gBAAgB;YAC9B,OAAO;gBACL,OAAO,IAAI,CAAC,eAAe;YAC7B;QACF;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,cAAc,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAChD;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}]}