{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Chat%20Application/chat-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Chat%20Application/chat-app/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'\n  size?: 'default' | 'sm' | 'lg' | 'icon'\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n          {\n            'bg-primary text-primary-foreground hover:bg-primary/90': variant === 'default',\n            'bg-destructive text-destructive-foreground hover:bg-destructive/90': variant === 'destructive',\n            'border border-input bg-background hover:bg-accent hover:text-accent-foreground': variant === 'outline',\n            'bg-secondary text-secondary-foreground hover:bg-secondary/80': variant === 'secondary',\n            'hover:bg-accent hover:text-accent-foreground': variant === 'ghost',\n            'text-primary underline-offset-4 hover:underline': variant === 'link',\n          },\n          {\n            'h-10 px-4 py-2': size === 'default',\n            'h-9 rounded-md px-3': size === 'sm',\n            'h-11 rounded-md px-8': size === 'lg',\n            'h-10 w-10': size === 'icon',\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0RACA;YACE,0DAA0D,YAAY;YACtE,sEAAsE,YAAY;YAClF,kFAAkF,YAAY;YAC9F,gEAAgE,YAAY;YAC5E,gDAAgD,YAAY;YAC5D,mDAAmD,YAAY;QACjE,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Chat%20Application/chat-app/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport Link from 'next/link'\nimport { MessageCircle, Users, Shield, Zap, AlertCircle, ExternalLink } from 'lucide-react'\nimport { Button } from '@/components/ui/Button'\n\nexport default function Home() {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n  const [isDemoMode, setIsDemoMode] = useState(false)\n\n  useEffect(() => {\n    // Check if we're in demo mode\n    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\n    setIsDemoMode(!supabaseUrl || supabaseUrl === 'https://demo.supabase.co')\n\n    if (!loading && user) {\n      router.push('/chat')\n    }\n  }, [user, loading, router])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"container mx-auto px-4 py-16\">\n        {/* Demo Mode Warning */}\n        {isDemoMode && (\n          <div className=\"mb-8 mx-auto max-w-4xl\">\n            <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n              <div className=\"flex items-start space-x-3\">\n                <AlertCircle className=\"h-5 w-5 text-yellow-600 mt-0.5\" />\n                <div>\n                  <h3 className=\"text-sm font-medium text-yellow-800\">Demo Mode Active</h3>\n                  <p className=\"text-sm text-yellow-700 mt-1\">\n                    The application is running in demo mode. To enable full functionality including authentication and database features,\n                    please set up Supabase by following the instructions in the README.\n                  </p>\n                  <div className=\"mt-3\">\n                    <a\n                      href=\"https://supabase.com\"\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"inline-flex items-center text-sm text-yellow-800 hover:text-yellow-900\"\n                    >\n                      Get started with Supabase\n                      <ExternalLink className=\"ml-1 h-3 w-3\" />\n                    </a>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div className=\"text-center mb-16\">\n          <h1 className=\"text-5xl font-bold text-gray-900 mb-6\">\n            Welcome to <span className=\"text-blue-600\">ChatApp</span>\n          </h1>\n          <p className=\"text-xl text-gray-600 mb-8 max-w-2xl mx-auto\">\n            Connect with friends, join chat rooms, share media, and experience real-time messaging like never before.\n          </p>\n          <div className=\"flex gap-4 justify-center\">\n            {isDemoMode ? (\n              <div className=\"space-y-4\">\n                <div className=\"flex gap-4 justify-center\">\n                  <Button size=\"lg\" className=\"px-8\" disabled>\n                    Get Started (Setup Required)\n                  </Button>\n                  <Button variant=\"outline\" size=\"lg\" className=\"px-8\" disabled>\n                    Sign In (Setup Required)\n                  </Button>\n                </div>\n                <p className=\"text-sm text-gray-500\">\n                  Set up Supabase to enable authentication and start chatting\n                </p>\n              </div>\n            ) : (\n              <>\n                <Link href=\"/auth/register\">\n                  <Button size=\"lg\" className=\"px-8\">\n                    Get Started\n                  </Button>\n                </Link>\n                <Link href=\"/auth/login\">\n                  <Button variant=\"outline\" size=\"lg\" className=\"px-8\">\n                    Sign In\n                  </Button>\n                </Link>\n              </>\n            )}\n          </div>\n        </div>\n\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\">\n          <div className=\"text-center p-6 bg-white rounded-lg shadow-sm\">\n            <MessageCircle className=\"h-12 w-12 text-blue-600 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold mb-2\">Real-time Messaging</h3>\n            <p className=\"text-gray-600\">Instant messaging with typing indicators and delivery status</p>\n          </div>\n\n          <div className=\"text-center p-6 bg-white rounded-lg shadow-sm\">\n            <Users className=\"h-12 w-12 text-green-600 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold mb-2\">Chat Rooms</h3>\n            <p className=\"text-gray-600\">Join public rooms or create private spaces for your team</p>\n          </div>\n\n          <div className=\"text-center p-6 bg-white rounded-lg shadow-sm\">\n            <Shield className=\"h-12 w-12 text-purple-600 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold mb-2\">Private Messages</h3>\n            <p className=\"text-gray-600\">Secure one-on-one conversations with end-to-end encryption</p>\n          </div>\n\n          <div className=\"text-center p-6 bg-white rounded-lg shadow-sm\">\n            <Zap className=\"h-12 w-12 text-orange-600 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold mb-2\">Media Sharing</h3>\n            <p className=\"text-gray-600\">Share images, videos, documents, and more with ease</p>\n          </div>\n        </div>\n\n        <div className=\"text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Ready to start chatting?</h2>\n          <p className=\"text-gray-600 mb-8\">Join thousands of users already connecting on ChatApp</p>\n          {isDemoMode ? (\n            <div className=\"space-y-4\">\n              <Button size=\"lg\" className=\"px-12\" disabled>\n                Create Your Account (Setup Required)\n              </Button>\n              <div className=\"bg-white rounded-lg p-6 max-w-md mx-auto\">\n                <h3 className=\"font-semibold text-gray-900 mb-2\">Quick Setup Guide:</h3>\n                <ol className=\"text-left text-sm text-gray-600 space-y-1\">\n                  <li>1. Create a free Supabase account</li>\n                  <li>2. Run the SQL schema from supabase-schema.sql</li>\n                  <li>3. Update .env.local with your Supabase credentials</li>\n                  <li>4. Restart the application</li>\n                </ol>\n              </div>\n            </div>\n          ) : (\n            <Link href=\"/auth/register\">\n              <Button size=\"lg\" className=\"px-12\">\n                Create Your Account\n              </Button>\n            </Link>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8BAA8B;QAC9B,MAAM;QACN,cAAc,CAAC,eAAe,gBAAgB;QAE9C,IAAI,CAAC,WAAW,MAAM;YACpB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;gBAEZ,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,8OAAC;4CAAE,WAAU;sDAA+B;;;;;;sDAI5C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;;oDACX;kEAEC,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAStC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAwC;8CACzC,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAE7C,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAG5D,8OAAC;4BAAI,WAAU;sCACZ,2BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,WAAU;gDAAO,QAAQ;0DAAC;;;;;;0DAG5C,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;gDAAO,QAAQ;0DAAC;;;;;;;;;;;;kDAIhE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;qDAKvC;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAAO;;;;;;;;;;;kDAIrC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAAO;;;;;;;;;;;;;;;;;;;;;;;;8BAS/D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAG/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAG/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAG/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;8BAIjC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;wBACjC,2BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;oCAAQ,QAAQ;8CAAC;;;;;;8CAG7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;iDAKV,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,WAAU;0CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlD", "debugId": null}}]}