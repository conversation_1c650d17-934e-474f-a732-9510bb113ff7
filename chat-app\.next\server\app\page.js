/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cheman%5COneDrive%5CDesktop%5CChat%20Application%5Cchat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cheman%5COneDrive%5CDesktop%5CChat%20Application%5Cchat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cheman%5COneDrive%5CDesktop%5CChat%20Application%5Cchat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cheman%5COneDrive%5CDesktop%5CChat%20Application%5Cchat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cheman%5COneDrive%5CDesktop%5CChat%20Application%5Cchat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cheman%5COneDrive%5CDesktop%5CChat%20Application%5Cchat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Ccontexts%5C%5CSocketContext.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Ccontexts%5C%5CSocketContext.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/SocketContext.tsx */ \"(rsc)/./src/contexts/SocketContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hlbWFuJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDQ2hhdCUyMEFwcGxpY2F0aW9uJTVDJTVDY2hhdC1hcHAlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkdlaXN0JTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtZ2Vpc3Qtc2FucyU1QyUyMiUyQyU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmdlaXN0U2FucyU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNoZW1hbiU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q0NoYXQlMjBBcHBsaWNhdGlvbiU1QyU1Q2NoYXQtYXBwJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJHZWlzdF9Nb25vJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtZ2Vpc3QtbW9ubyU1QyUyMiUyQyU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmdlaXN0TW9ubyU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNoZW1hbiU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q0NoYXQlMjBBcHBsaWNhdGlvbiU1QyU1Q2NoYXQtYXBwJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDcmVhY3QtaG90LXRvYXN0JTVDJTVDZGlzdCU1QyU1Q2luZGV4Lm1qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRvYXN0ZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDaGVtYW4lNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNDaGF0JTIwQXBwbGljYXRpb24lNUMlNUNjaGF0LWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hlbWFuJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDQ2hhdCUyMEFwcGxpY2F0aW9uJTVDJTVDY2hhdC1hcHAlNUMlNUNzcmMlNUMlNUNjb250ZXh0cyU1QyU1Q0F1dGhDb250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNoZW1hbiU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q0NoYXQlMjBBcHBsaWNhdGlvbiU1QyU1Q2NoYXQtYXBwJTVDJTVDc3JjJTVDJTVDY29udGV4dHMlNUMlNUNTb2NrZXRDb250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNvY2tldFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzTUFBaUw7QUFDakw7QUFDQSx3S0FBc0s7QUFDdEs7QUFDQSw0S0FBMEsiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRvYXN0ZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxoZW1hblxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXENoYXQgQXBwbGljYXRpb25cXFxcY2hhdC1hcHBcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWhvdC10b2FzdFxcXFxkaXN0XFxcXGluZGV4Lm1qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcaGVtYW5cXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxDaGF0IEFwcGxpY2F0aW9uXFxcXGNoYXQtYXBwXFxcXHNyY1xcXFxjb250ZXh0c1xcXFxBdXRoQ29udGV4dC50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlNvY2tldFByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcaGVtYW5cXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxDaGF0IEFwcGxpY2F0aW9uXFxcXGNoYXQtYXBwXFxcXHNyY1xcXFxjb250ZXh0c1xcXFxTb2NrZXRDb250ZXh0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Ccontexts%5C%5CSocketContext.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hlbWFuJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDQ2hhdCUyMEFwcGxpY2F0aW9uJTVDJTVDY2hhdC1hcHAlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQXdIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxoZW1hblxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXENoYXQgQXBwbGljYXRpb25cXFxcY2hhdC1hcHBcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaGVtYW5cXE9uZURyaXZlXFxEZXNrdG9wXFxDaGF0IEFwcGxpY2F0aW9uXFxjaGF0LWFwcFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"cc7adede8753\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhlbWFuXFxPbmVEcml2ZVxcRGVza3RvcFxcQ2hhdCBBcHBsaWNhdGlvblxcY2hhdC1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImNjN2FkZWRlODc1M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_SocketContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/SocketContext */ \"(rsc)/./src/contexts/SocketContext.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Chat Application\",\n    description: \"A modern real-time chat application with rooms and private messaging\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_SocketContext__WEBPACK_IMPORTED_MODULE_3__.SocketProvider, {\n                    children: [\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.Toaster, {\n                            position: \"top-right\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Chat Application\\chat-app\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Chat Application\\chat-app\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Chat Application\\chat-app\\src\\contexts\\AuthContext.tsx",
"useAuth",
);

/***/ }),

/***/ "(rsc)/./src/contexts/SocketContext.tsx":
/*!****************************************!*\
  !*** ./src/contexts/SocketContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SocketProvider: () => (/* binding */ SocketProvider),
/* harmony export */   useSocket: () => (/* binding */ useSocket)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const SocketProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SocketProvider() from the server but SocketProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Chat Application\\chat-app\\src\\contexts\\SocketContext.tsx",
"SocketProvider",
);const useSocket = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useSocket() from the server but useSocket is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Chat Application\\chat-app\\src\\contexts\\SocketContext.tsx",
"useSocket",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Ccontexts%5C%5CSocketContext.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Ccontexts%5C%5CSocketContext.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/SocketContext.tsx */ \"(ssr)/./src/contexts/SocketContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Ccontexts%5C%5CSocketContext.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hlbWFuJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDQ2hhdCUyMEFwcGxpY2F0aW9uJTVDJTVDY2hhdC1hcHAlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQXdIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxoZW1hblxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXENoYXQgQXBwbGljYXRpb25cXFxcY2hhdC1hcHBcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cheman%5C%5COneDrive%5C%5CDesktop%5C%5CChat%20Application%5C%5Cchat-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ExternalLink_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ExternalLink,MessageCircle,Shield,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ExternalLink_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ExternalLink,MessageCircle,Shield,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ExternalLink_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ExternalLink,MessageCircle,Shield,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ExternalLink_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ExternalLink,MessageCircle,Shield,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ExternalLink_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ExternalLink,MessageCircle,Shield,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ExternalLink_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ExternalLink,MessageCircle,Shield,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Home() {\n    const { user, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isDemoMode, setIsDemoMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // Check if we're in demo mode\n            const supabaseUrl = \"https://demo.supabase.co\";\n            setIsDemoMode(!supabaseUrl || supabaseUrl === 'https://demo.supabase.co');\n            if (!loading && user) {\n                router.push('/chat');\n            }\n        }\n    }[\"Home.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-16\",\n            children: [\n                isDemoMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8 mx-auto max-w-4xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ExternalLink_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5 text-yellow-600 mt-0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-yellow-800\",\n                                            children: \"Demo Mode Active\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-yellow-700 mt-1\",\n                                            children: \"The application is running in demo mode. To enable full functionality including authentication and database features, please set up Supabase by following the instructions in the README.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"https://supabase.com\",\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"inline-flex items-center text-sm text-yellow-800 hover:text-yellow-900\",\n                                                children: [\n                                                    \"Get started with Supabase\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ExternalLink_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"ml-1 h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 56,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl font-bold text-gray-900 mb-6\",\n                            children: [\n                                \"Welcome to \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-blue-600\",\n                                    children: \"ChatApp\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 24\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 mb-8 max-w-2xl mx-auto\",\n                            children: \"Connect with friends, join chat rooms, share media, and experience real-time messaging like never before.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-4 justify-center\",\n                            children: isDemoMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4 justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                size: \"lg\",\n                                                className: \"px-8\",\n                                                disabled: true,\n                                                children: \"Get Started (Setup Required)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"outline\",\n                                                size: \"lg\",\n                                                className: \"px-8\",\n                                                disabled: true,\n                                                children: \"Sign In (Setup Required)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Set up Supabase to enable authentication and start chatting\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/auth/register\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            size: \"lg\",\n                                            className: \"px-8\",\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/auth/login\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            size: \"lg\",\n                                            className: \"px-8\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center p-6 bg-white rounded-lg shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ExternalLink_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-12 w-12 text-blue-600 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-2\",\n                                    children: \"Real-time Messaging\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Instant messaging with typing indicators and delivery status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center p-6 bg-white rounded-lg shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ExternalLink_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-12 w-12 text-green-600 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-2\",\n                                    children: \"Chat Rooms\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Join public rooms or create private spaces for your team\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center p-6 bg-white rounded-lg shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ExternalLink_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-12 w-12 text-purple-600 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-2\",\n                                    children: \"Private Messages\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Secure one-on-one conversations with end-to-end encryption\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center p-6 bg-white rounded-lg shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ExternalLink_MessageCircle_Shield_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-12 w-12 text-orange-600 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-2\",\n                                    children: \"Media Sharing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Share images, videos, documents, and more with ease\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-4\",\n                            children: \"Ready to start chatting?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-8\",\n                            children: \"Join thousands of users already connecting on ChatApp\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this),\n                        isDemoMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    size: \"lg\",\n                                    className: \"px-12\",\n                                    disabled: true,\n                                    children: \"Create Your Account (Setup Required)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-6 max-w-md mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Quick Setup Guide:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                            className: \"text-left text-sm text-gray-600 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"1. Create a free Supabase account\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"2. Run the SQL schema from supabase-schema.sql\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"3. Update .env.local with your Supabase credentials\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"4. Restart the application\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                            href: \"/auth/register\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                size: \"lg\",\n                                className: \"px-12\",\n                                children: \"Create Your Account\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Button = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, variant = 'default', size = 'default', ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50', {\n            'bg-primary text-primary-foreground hover:bg-primary/90': variant === 'default',\n            'bg-destructive text-destructive-foreground hover:bg-destructive/90': variant === 'destructive',\n            'border border-input bg-background hover:bg-accent hover:text-accent-foreground': variant === 'outline',\n            'bg-secondary text-secondary-foreground hover:bg-secondary/80': variant === 'secondary',\n            'hover:bg-accent hover:text-accent-foreground': variant === 'ghost',\n            'text-primary underline-offset-4 hover:underline': variant === 'link'\n        }, {\n            'h-10 px-4 py-2': size === 'default',\n            'h-9 rounded-md px-3': size === 'sm',\n            'h-11 rounded-md px-8': size === 'lg',\n            'h-10 w-10': size === 'icon'\n        }, className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 12,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = 'Button';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [supabaseUser, setSupabaseUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Get initial session\n            const getInitialSession = {\n                \"AuthProvider.useEffect.getInitialSession\": async ()=>{\n                    const { data: { session } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n                    if (session?.user) {\n                        setSupabaseUser(session.user);\n                        await fetchUserProfile(session.user.id);\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.getInitialSession\"];\n            getInitialSession();\n            // Listen for auth changes\n            const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    if (session?.user) {\n                        setSupabaseUser(session.user);\n                        await fetchUserProfile(session.user.id);\n                    } else {\n                        setSupabaseUser(null);\n                        setUser(null);\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>subscription.unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const fetchUserProfile = async (userId)=>{\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('users').select('*').eq('id', userId).single();\n            if (error) throw error;\n            setUser(data);\n        } catch (error) {\n            console.error('Error fetching user profile:', error);\n        }\n    };\n    const signIn = async (email, password)=>{\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) return {\n                error: error.message\n            };\n            return {};\n        } catch (error) {\n            return {\n                error: 'An unexpected error occurred'\n            };\n        }\n    };\n    const signUp = async (email, password, username)=>{\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: {\n                        username\n                    }\n                }\n            });\n            if (error) return {\n                error: error.message\n            };\n            return {};\n        } catch (error) {\n            return {\n                error: 'An unexpected error occurred'\n            };\n        }\n    };\n    const signOut = async ()=>{\n        await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n        setUser(null);\n        setSupabaseUser(null);\n        router.push('/auth/login');\n    };\n    const updateProfile = async (updates)=>{\n        if (!user) return {\n            error: 'No user logged in'\n        };\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('users').update({\n                ...updates,\n                updated_at: new Date().toISOString()\n            }).eq('id', user.id);\n            if (error) throw error;\n            setUser({\n                ...user,\n                ...updates\n            });\n            return {};\n        } catch (error) {\n            return {\n                error: 'Failed to update profile'\n            };\n        }\n    };\n    const value = {\n        user,\n        supabaseUser,\n        loading,\n        signIn,\n        signUp,\n        signOut,\n        updateProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 139,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/SocketContext.tsx":
/*!****************************************!*\
  !*** ./src/contexts/SocketContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SocketProvider: () => (/* binding */ SocketProvider),\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ SocketProvider,useSocket auto */ \n\n\n\nconst SocketContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction SocketProvider({ children }) {\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [onlineUsers, setOnlineUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [typingUsers, setTypingUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SocketProvider.useEffect\": ()=>{\n            if (user) {\n                const socketUrl = \"http://localhost:3006\" || 0;\n                const newSocket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(socketUrl, {\n                    auth: {\n                        userId: user.id,\n                        username: user.username\n                    }\n                });\n                setSocket(newSocket);\n                // Listen for online users updates\n                newSocket.on('users:online', {\n                    \"SocketProvider.useEffect\": (users)=>{\n                        setOnlineUsers(users);\n                    }\n                }[\"SocketProvider.useEffect\"]);\n                // Listen for typing indicators\n                newSocket.on('typing:start', {\n                    \"SocketProvider.useEffect\": ({ roomId, userId, username })=>{\n                        setTypingUsers({\n                            \"SocketProvider.useEffect\": (prev)=>({\n                                    ...prev,\n                                    [roomId]: [\n                                        ...(prev[roomId] || []).filter({\n                                            \"SocketProvider.useEffect\": (u)=>u !== username\n                                        }[\"SocketProvider.useEffect\"]),\n                                        username\n                                    ]\n                                })\n                        }[\"SocketProvider.useEffect\"]);\n                    }\n                }[\"SocketProvider.useEffect\"]);\n                newSocket.on('typing:stop', {\n                    \"SocketProvider.useEffect\": ({ roomId, userId, username })=>{\n                        setTypingUsers({\n                            \"SocketProvider.useEffect\": (prev)=>({\n                                    ...prev,\n                                    [roomId]: (prev[roomId] || []).filter({\n                                        \"SocketProvider.useEffect\": (u)=>u !== username\n                                    }[\"SocketProvider.useEffect\"])\n                                })\n                        }[\"SocketProvider.useEffect\"]);\n                    }\n                }[\"SocketProvider.useEffect\"]);\n                // Clean up typing indicators after timeout\n                newSocket.on('typing:timeout', {\n                    \"SocketProvider.useEffect\": ({ roomId, username })=>{\n                        setTypingUsers({\n                            \"SocketProvider.useEffect\": (prev)=>({\n                                    ...prev,\n                                    [roomId]: (prev[roomId] || []).filter({\n                                        \"SocketProvider.useEffect\": (u)=>u !== username\n                                    }[\"SocketProvider.useEffect\"])\n                                })\n                        }[\"SocketProvider.useEffect\"]);\n                    }\n                }[\"SocketProvider.useEffect\"]);\n                return ({\n                    \"SocketProvider.useEffect\": ()=>{\n                        newSocket.close();\n                    }\n                })[\"SocketProvider.useEffect\"];\n            }\n        }\n    }[\"SocketProvider.useEffect\"], [\n        user\n    ]);\n    const joinRoom = (roomId)=>{\n        if (socket) {\n            socket.emit('room:join', roomId);\n        }\n    };\n    const leaveRoom = (roomId)=>{\n        if (socket) {\n            socket.emit('room:leave', roomId);\n        }\n    };\n    const sendMessage = (message)=>{\n        if (socket) {\n            socket.emit('message:send', message);\n        }\n    };\n    const sendPrivateMessage = (message)=>{\n        if (socket) {\n            socket.emit('message:private', message);\n        }\n    };\n    const sendTyping = (roomId, isTyping)=>{\n        if (socket) {\n            if (isTyping) {\n                socket.emit('typing:start', roomId);\n            } else {\n                socket.emit('typing:stop', roomId);\n            }\n        }\n    };\n    const value = {\n        socket,\n        onlineUsers,\n        typingUsers,\n        joinRoom,\n        leaveRoom,\n        sendMessage,\n        sendTyping,\n        sendPrivateMessage\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SocketContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Chat Application\\\\chat-app\\\\src\\\\contexts\\\\SocketContext.tsx\",\n        lineNumber: 118,\n        columnNumber: 10\n    }, this);\n}\nfunction useSocket() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SocketContext);\n    if (context === undefined) {\n        throw new Error('useSocket must be used within a SocketProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/SocketContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\n\nconst supabaseUrl = \"https://demo.supabase.co\" || 0;\nconst supabaseAnonKey = \"demo_key\" || 0;\n// Check if we're in demo mode\nconst isDemoMode = supabaseUrl === 'https://demo.supabase.co';\n// Client-side Supabase client (mock for demo mode)\nconst supabase = isDemoMode ? createMockSupabaseClient() : (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey);\n// Server-side Supabase client (for API routes)\nconst supabaseAdmin = isDemoMode ? createMockSupabaseClient() : (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, process.env.SUPABASE_SERVICE_ROLE_KEY);\n// Mock Supabase client for demo mode\nfunction createMockSupabaseClient() {\n    return {\n        auth: {\n            getSession: ()=>Promise.resolve({\n                    data: {\n                        session: null\n                    },\n                    error: null\n                }),\n            onAuthStateChange: (callback)=>{\n                // Return a mock subscription\n                return {\n                    data: {\n                        subscription: {\n                            unsubscribe: ()=>{}\n                        }\n                    }\n                };\n            },\n            signInWithPassword: ()=>Promise.resolve({\n                    error: {\n                        message: 'Demo mode: Please set up Supabase to enable authentication'\n                    }\n                }),\n            signUp: ()=>Promise.resolve({\n                    error: {\n                        message: 'Demo mode: Please set up Supabase to enable authentication'\n                    }\n                }),\n            signOut: ()=>Promise.resolve({\n                    error: null\n                })\n        },\n        from: (table)=>({\n                select: ()=>({\n                        eq: ()=>({\n                                single: ()=>Promise.resolve({\n                                        data: null,\n                                        error: {\n                                            message: 'Demo mode: Please set up Supabase database'\n                                        }\n                                    }),\n                                order: ()=>({\n                                        limit: ()=>Promise.resolve({\n                                                data: [],\n                                                error: null\n                                            })\n                                    })\n                            }),\n                        order: ()=>({\n                                limit: ()=>Promise.resolve({\n                                        data: [],\n                                        error: null\n                                    })\n                            }),\n                        limit: ()=>Promise.resolve({\n                                data: [],\n                                error: null\n                            })\n                    }),\n                insert: ()=>({\n                        select: ()=>({\n                                single: ()=>Promise.resolve({\n                                        data: null,\n                                        error: {\n                                            message: 'Demo mode: Please set up Supabase database'\n                                        }\n                                    })\n                            })\n                    }),\n                update: ()=>({\n                        eq: ()=>Promise.resolve({\n                                error: {\n                                    message: 'Demo mode: Please set up Supabase database'\n                                }\n                            })\n                    })\n            })\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxoZW1hblxcT25lRHJpdmVcXERlc2t0b3BcXENoYXQgQXBwbGljYXRpb25cXGNoYXQtYXBwXFxzcmNcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSAnY2xzeCdcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tICd0YWlsd2luZC1tZXJnZSdcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?abbf":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?ed2e":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/engine.io-client","vendor-chunks/ws","vendor-chunks/socket.io-client","vendor-chunks/whatwg-url","vendor-chunks/socket.io-parser","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/react-hot-toast","vendor-chunks/engine.io-parser","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/@socket.io","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/isows","vendor-chunks/has-flag","vendor-chunks/lucide-react","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cheman%5COneDrive%5CDesktop%5CChat%20Application%5Cchat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cheman%5COneDrive%5CDesktop%5CChat%20Application%5Cchat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();