(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/supabase.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "supabase": (()=>supabase),
    "supabaseAdmin": (()=>supabaseAdmin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-client] (ecmascript) <module evaluation>");
;
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://demo.supabase.co") || 'https://demo.supabase.co';
const supabaseAnonKey = ("TURBOPACK compile-time value", "demo_key") || 'demo_key';
// Check if we're in demo mode
const isDemoMode = supabaseUrl === 'https://demo.supabase.co';
const supabase = ("TURBOPACK compile-time truthy", 1) ? createMockSupabaseClient() : ("TURBOPACK unreachable", undefined);
const supabaseAdmin = ("TURBOPACK compile-time truthy", 1) ? createMockSupabaseClient() : ("TURBOPACK unreachable", undefined);
// Mock Supabase client for demo mode
function createMockSupabaseClient() {
    return {
        auth: {
            getSession: ()=>Promise.resolve({
                    data: {
                        session: null
                    },
                    error: null
                }),
            onAuthStateChange: (callback)=>{
                // Return a mock subscription
                return {
                    data: {
                        subscription: {
                            unsubscribe: ()=>{}
                        }
                    }
                };
            },
            signInWithPassword: ()=>Promise.resolve({
                    error: {
                        message: 'Demo mode: Please set up Supabase to enable authentication'
                    }
                }),
            signUp: ()=>Promise.resolve({
                    error: {
                        message: 'Demo mode: Please set up Supabase to enable authentication'
                    }
                }),
            signOut: ()=>Promise.resolve({
                    error: null
                })
        },
        from: (table)=>({
                select: ()=>({
                        eq: ()=>({
                                single: ()=>Promise.resolve({
                                        data: null,
                                        error: {
                                            message: 'Demo mode: Please set up Supabase database'
                                        }
                                    }),
                                order: ()=>({
                                        limit: ()=>Promise.resolve({
                                                data: [],
                                                error: null
                                            })
                                    })
                            }),
                        order: ()=>({
                                limit: ()=>Promise.resolve({
                                        data: [],
                                        error: null
                                    })
                            }),
                        limit: ()=>Promise.resolve({
                                data: [],
                                error: null
                            })
                    }),
                insert: ()=>({
                        select: ()=>({
                                single: ()=>Promise.resolve({
                                        data: null,
                                        error: {
                                            message: 'Demo mode: Please set up Supabase database'
                                        }
                                    })
                            })
                    }),
                update: ()=>({
                        eq: ()=>Promise.resolve({
                                error: {
                                    message: 'Demo mode: Please set up Supabase database'
                                }
                            })
                    })
            })
    };
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    _s();
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [supabaseUser, setSupabaseUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            // Get initial session
            const getInitialSession = {
                "AuthProvider.useEffect.getInitialSession": async ()=>{
                    const { data: { session } } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.getSession();
                    if (session?.user) {
                        setSupabaseUser(session.user);
                        await fetchUserProfile(session.user.id);
                    }
                    setLoading(false);
                }
            }["AuthProvider.useEffect.getInitialSession"];
            getInitialSession();
            // Listen for auth changes
            const { data: { subscription } } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.onAuthStateChange({
                "AuthProvider.useEffect": async (event, session)=>{
                    if (session?.user) {
                        setSupabaseUser(session.user);
                        await fetchUserProfile(session.user.id);
                    } else {
                        setSupabaseUser(null);
                        setUser(null);
                    }
                    setLoading(false);
                }
            }["AuthProvider.useEffect"]);
            return ({
                "AuthProvider.useEffect": ()=>subscription.unsubscribe()
            })["AuthProvider.useEffect"];
        }
    }["AuthProvider.useEffect"], []);
    const fetchUserProfile = async (userId)=>{
        try {
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('users').select('*').eq('id', userId).single();
            if (error) throw error;
            setUser(data);
        } catch (error) {
            console.error('Error fetching user profile:', error);
        }
    };
    const signIn = async (email, password)=>{
        try {
            const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.signInWithPassword({
                email,
                password
            });
            if (error) return {
                error: error.message
            };
            return {};
        } catch (error) {
            return {
                error: 'An unexpected error occurred'
            };
        }
    };
    const signUp = async (email, password, username)=>{
        try {
            const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.signUp({
                email,
                password,
                options: {
                    data: {
                        username
                    }
                }
            });
            if (error) return {
                error: error.message
            };
            return {};
        } catch (error) {
            return {
                error: 'An unexpected error occurred'
            };
        }
    };
    const signOut = async ()=>{
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.signOut();
        setUser(null);
        setSupabaseUser(null);
        router.push('/auth/login');
    };
    const updateProfile = async (updates)=>{
        if (!user) return {
            error: 'No user logged in'
        };
        try {
            const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('users').update({
                ...updates,
                updated_at: new Date().toISOString()
            }).eq('id', user.id);
            if (error) throw error;
            setUser({
                ...user,
                ...updates
            });
            return {};
        } catch (error) {
            return {
                error: 'Failed to update profile'
            };
        }
    };
    const value = {
        user,
        supabaseUser,
        loading,
        signIn,
        signUp,
        signOut,
        updateProfile
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 139,
        columnNumber: 10
    }, this);
}
_s(AuthProvider, "dpRyHdf2/Pmqcw2miprUW9ujm80=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = AuthProvider;
function useAuth() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
_s1(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/SocketContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "SocketProvider": (()=>SocketProvider),
    "useSocket": (()=>useSocket)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
const SocketContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function SocketProvider({ children }) {
    _s();
    const [socket, setSocket] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [onlineUsers, setOnlineUsers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [typingUsers, setTypingUsers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const { user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SocketProvider.useEffect": ()=>{
            // Always try to connect to Socket.io server, even in demo mode
            const socketUrl = ("TURBOPACK compile-time value", "http://localhost:3009") || 'http://localhost:3008';
            console.log('Connecting to Socket.io server at:', socketUrl);
            const newSocket = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["io"])(socketUrl, {
                auth: {
                    userId: user?.id || 'demo-user',
                    username: user?.username || 'Demo User'
                },
                transports: [
                    'websocket',
                    'polling'
                ],
                timeout: 20000,
                forceNew: true
            });
            // Connection event handlers
            newSocket.on('connect', {
                "SocketProvider.useEffect": ()=>{
                    console.log('✅ Connected to Socket.io server');
                }
            }["SocketProvider.useEffect"]);
            newSocket.on('connect_error', {
                "SocketProvider.useEffect": (error)=>{
                    console.error('❌ Socket.io connection error:', error);
                }
            }["SocketProvider.useEffect"]);
            newSocket.on('disconnect', {
                "SocketProvider.useEffect": (reason)=>{
                    console.log('🔌 Disconnected from Socket.io server:', reason);
                }
            }["SocketProvider.useEffect"]);
            setSocket(newSocket);
            // Listen for online users updates
            newSocket.on('users:online', {
                "SocketProvider.useEffect": (users)=>{
                    setOnlineUsers(users);
                }
            }["SocketProvider.useEffect"]);
            // Listen for typing indicators
            newSocket.on('typing:start', {
                "SocketProvider.useEffect": ({ roomId, userId, username })=>{
                    setTypingUsers({
                        "SocketProvider.useEffect": (prev)=>({
                                ...prev,
                                [roomId]: [
                                    ...(prev[roomId] || []).filter({
                                        "SocketProvider.useEffect": (u)=>u !== username
                                    }["SocketProvider.useEffect"]),
                                    username
                                ]
                            })
                    }["SocketProvider.useEffect"]);
                }
            }["SocketProvider.useEffect"]);
            newSocket.on('typing:stop', {
                "SocketProvider.useEffect": ({ roomId, userId, username })=>{
                    setTypingUsers({
                        "SocketProvider.useEffect": (prev)=>({
                                ...prev,
                                [roomId]: (prev[roomId] || []).filter({
                                    "SocketProvider.useEffect": (u)=>u !== username
                                }["SocketProvider.useEffect"])
                            })
                    }["SocketProvider.useEffect"]);
                }
            }["SocketProvider.useEffect"]);
            // Clean up typing indicators after timeout
            newSocket.on('typing:timeout', {
                "SocketProvider.useEffect": ({ roomId, username })=>{
                    setTypingUsers({
                        "SocketProvider.useEffect": (prev)=>({
                                ...prev,
                                [roomId]: (prev[roomId] || []).filter({
                                    "SocketProvider.useEffect": (u)=>u !== username
                                }["SocketProvider.useEffect"])
                            })
                    }["SocketProvider.useEffect"]);
                }
            }["SocketProvider.useEffect"]);
            return ({
                "SocketProvider.useEffect": ()=>{
                    newSocket.close();
                }
            })["SocketProvider.useEffect"];
        }
    }["SocketProvider.useEffect"], []) // Remove user dependency to allow connection in demo mode
    ;
    const joinRoom = (roomId)=>{
        if (socket) {
            socket.emit('room:join', roomId);
        }
    };
    const leaveRoom = (roomId)=>{
        if (socket) {
            socket.emit('room:leave', roomId);
        }
    };
    const sendMessage = (message)=>{
        if (socket) {
            socket.emit('message:send', message);
        }
    };
    const sendPrivateMessage = (message)=>{
        if (socket) {
            socket.emit('message:private', message);
        }
    };
    const sendTyping = (roomId, isTyping)=>{
        if (socket) {
            if (isTyping) {
                socket.emit('typing:start', roomId);
            } else {
                socket.emit('typing:stop', roomId);
            }
        }
    };
    const value = {
        socket,
        onlineUsers,
        typingUsers,
        joinRoom,
        leaveRoom,
        sendMessage,
        sendTyping,
        sendPrivateMessage
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SocketContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/SocketContext.tsx",
        lineNumber: 135,
        columnNumber: 10
    }, this);
}
_s(SocketProvider, "A3t8zimq3DcKdnvr2pXoSEL7MyY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = SocketProvider;
function useSocket() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(SocketContext);
    if (context === undefined) {
        throw new Error('useSocket must be used within a SocketProvider');
    }
    return context;
}
_s1(useSocket, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "SocketProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_18f46894._.js.map